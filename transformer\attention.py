# transformer/attention.py

import math
from utils.math_ops import dot_product, softmax, matmul, transpose

def attention(query, key, value):
    """
    query, key, value: List of vectors (length = sentence length, each vector = d_model)
    Output: Attention output vectors
    """
    d_k = len(query[0])  # embedding dimension

    # Step 1: Calculate attention scores (Q · K^T)
    key_T = transpose(key)                      # Transpose key for dot product
    scores = [                                  # Each query · all keys
        [dot_product(q, k_col) for k_col in key_T]
        for q in query
    ]

    # Step 2: Scale by sqrt(d_k)
    scale = math.sqrt(d_k)
    scaled_scores = [[s / scale for s in row] for row in scores]

    # Step 3: Apply softmax to get attention weights
    attention_weights = [softmax(row) for row in scaled_scores]

    # Step 4: Multiply weights with value vectors
    output = matmul(attention_weights, value)

    return output, attention_weights
