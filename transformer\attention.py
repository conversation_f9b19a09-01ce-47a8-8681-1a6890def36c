# transformer/attention.py

import math
from utils.math_ops import dot_product, softmax, matmul, transpose, split_heads, combine_heads

def apply_causal_mask(scores):
    """
    Sets future positions to -inf in attention scores to prevent attending to future tokens.
    scores: List of List of floats (shape: [seq_len x seq_len])
    """
    masked_scores = []
    for i, row in enumerate(scores):
        masked_row = [score if j <= i else float('-inf') for j, score in enumerate(row)]
        masked_scores.append(masked_row)
    return masked_scores

def attention(query, key, value, mask_type=None):
    d_k = len(query[0])  # embedding dimension
    seq_len = len(query)

    # Step 1: Attention scores
    key_T = transpose(key)
    scores = [
        [dot_product(q, k_col) for k_col in key_T]
        for q in query
    ]

    # Step 2: Scale
    scale = math.sqrt(d_k)
    scaled_scores = [[s / scale for s in row] for row in scores]

    # Step 3: Mask (optional)
    if mask_type == "causal":
        scaled_scores = apply_causal_mask(scaled_scores)

    # Step 4: Softmax
    attention_weights = [softmax(row) for row in scaled_scores]

    # Step 5: Weighted sum
    output = matmul(attention_weights, value)

    return output, attention_weights

def multi_head_attention(query, key, value, num_heads=2, mask_type=None):
    """
    Performs multi-head attention
    query, key, value: List of vectors
    """
    d_model = len(query[0])
    assert d_model % num_heads == 0, "d_model must be divisible by num_heads"
    depth = d_model // num_heads

    # Step 1: Split into heads
    q_heads = split_heads(query, num_heads)
    k_heads = split_heads(key, num_heads)
    v_heads = split_heads(value, num_heads)

    # Step 2: Apply attention for each head
    head_outputs = []
    all_weights = []
    for h in range(num_heads):
        out, weights = attention(q_heads[h], k_heads[h], v_heads[h], mask_type)
        head_outputs.append(out)
        all_weights.append(weights)

    # Step 3: Concatenate heads
    combined_output = combine_heads(head_outputs)

    return combined_output, all_weights
