# nlp/embedding.py

from utils.math_ops import initialize_matrix

class Embedding:
    def __init__(self, vocab_size, embedding_dim):
        self.vocab_size = vocab_size
        self.embedding_dim = embedding_dim
        self.embedding_matrix = initialize_matrix(vocab_size, embedding_dim)

    def get_vector(self, word_id):
        return self.embedding_matrix[word_id]

    def embed_sentence(self, sentence_ids):
        return [self.get_vector(idx) for idx in sentence_ids]

    def embed_batch(self, batch_ids):
        return [self.embed_sentence(sentence) for sentence in batch_ids]
