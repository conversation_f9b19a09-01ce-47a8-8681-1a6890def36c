# Feedforward layer used in encoder
# transformer/feedforward.py

# transformer/feedforward.py

import random
from utils.math_ops import add_vectors, dot_product, relu

class FeedForward:
    def __init__(self, d_model, d_ff):
        # Random small weights and biases (e.g., uniform in [-0.1, 0.1])
        self.W1 = [[random.uniform(-0.1, 0.1) for _ in range(d_ff)] for _ in range(d_model)]
        self.b1 = [random.uniform(-0.1, 0.1) for _ in range(d_ff)]

        self.W2 = [[random.uniform(-0.1, 0.1) for _ in range(d_model)] for _ in range(d_ff)]
        self.b2 = [random.uniform(-0.1, 0.1) for _ in range(d_model)]

    def linear(self, x, W, b):
        result = []
        for j in range(len(W[0])):  # output dimension
            val = 0
            for i in range(len(x)):
                val += x[i] * W[i][j]
            result.append(val + b[j])
        return result

    def forward(self, x):
        hidden = relu(self.linear(x, self.W1, self.b1))
        output = self.linear(hidden, self.W2, self.b2)
        return output

    def forward_batch(self, inputs):
        return [self.forward(x) for x in inputs]
