# transformer/encoder.py

from transformer.feedforward import <PERSON><PERSON><PERSON>orward
from transformer.self_attention import SelfAttention
from utils.math_ops import add_vectors

class EncoderLayer:
    def __init__(self, d_model, d_ff):
        self.self_attn = SelfAttention(d_model)
        self.ff = FeedForward(d_model, d_ff)

    def layer_norm(self, x):
        # Simple layer normalization
        mean = sum(x) / len(x)
        variance = sum((xi - mean) ** 2 for xi in x) / len(x)
        return [(xi - mean) / (variance + 1e-8) ** 0.5 for xi in x]

    def forward(self, x):
        # Self-attention with residual + layer norm
        attn_output = self.self_attn.forward(x)
        x = [add_vectors(orig, attn) for orig, attn in zip(x, attn_output)]
        x = [self.layer_norm(vec) for vec in x]
        
        # Feed-forward with residual + layer norm
        ff_output = self.ff.forward_batch(x)
        x = [add_vectors(orig, ff) for orig, ff in zip(x, ff_output)]
        x = [self.layer_norm(vec) for vec in x]
        
        return x

class Encoder:
    def __init__(self, num_layers, d_model, d_ff):
        self.layers = [EncoderLayer(d_model, d_ff) for _ in range(num_layers)]

    def forward(self, x):
        for layer in self.layers:
            x = layer.forward(x)
        return x

