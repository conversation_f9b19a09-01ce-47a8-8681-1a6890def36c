'''
from nlp.tokenizer import process_file
from nlp.embedding import Embedding

# Step 1: Tokenize and get indexed data
tokens, vocab, rev_vocab, indexed_data = process_file("data/raw_text.txt")

# Step 2: Create embedding instance
embedding_dim = 4  # Example: 4-dimensional vectors
embedder = Embedding(vocab_size=len(vocab), embedding_dim=embedding_dim)

# Step 3: Embed one sentence
sentence_ids = indexed_data[0]  # e.g., [0, 1, 2]
embedded = embedder.embed_sentence(sentence_ids)

# Output
print("Original IDs:", sentence_ids)
print("Embedded vectors:")
for vec in embedded:
    print(vec)

from nlp.positional_encoding import encode_sentence_positions

length = 5  # Length of sentence
embedding_dim = 4  # Must match embedding dim
positional_vectors = encode_sentence_positions(length, embedding_dim)

print("Positional Encoding Vectors:")
for vec in positional_vectors:
    print(vec)
from transformer.attention import attention

# Fake inputs (3 tokens, 4-dim embeddings)
query = [[1, 0, 1, 0], [0, 1, 0, 1], [1, 1, 1, 1]]
key   = [[1, 0, 1, 0], [0, 1, 0, 1], [1, 1, 1, 1]]
value = [[0.1, 0.2, 0.3, 0.4], [0.5, 0.6, 0.7, 0.8], [0.9, 1.0, 1.1, 1.2]]

output, weights = attention(query, key, value)

print("Attention Output:")
for vec in output:
    print(vec)

print("\nAttention Weights:")
for row in weights:
    print(row)
from transformer.feedforward import FeedForward

# One input token (4-dim embedding)
input_vec = [1.0, 0.5, 0.2, 0.1]

# Create feedforward with d_model=4, d_ff=8
ffn = FeedForward(d_model=4, d_ff=8)

# Pass through FFN
output_vec = ffn.forward(input_vec)

print("Output Vector:", output_vec)
'''
# --- Save vocab to file ---
def save_vocab(vocab, filepath="data/vocab.txt"):
    with open(filepath, "w", encoding="utf-8") as f:
        for word, idx in vocab.items():
            f.write(f"{word} {idx}\n")

# --- Save indexed sentences to file ---
def save_indexed_data(indexed_data, filepath="data/preprocessed_data.txt"):
    with open(filepath, "w", encoding="utf-8") as f:
        for sentence in indexed_data:
            f.write(" ".join(map(str, sentence)) + "\n")


from nlp.tokenizer import process_file
from nlp.embedding import Embedding
from nlp.positional_encoding import encode_sentence_positions
from transformer.encoder import Encoder

def main():
    # Set random seed for consistent results
    import random
    random.seed(42)

    # Step 1: Tokenization
    tokens, vocab, rev_vocab, indexed_data = process_file("data/raw_text.txt")
    print("\n=== Tokenization ===")
    #print("Tokens:", tokens)
    #print("Vocab:", vocab)
    #print("Indexed Data:", indexed_data)
    save_vocab(vocab, "data/vocab.txt")
    save_indexed_data(indexed_data, "data/preprocessed_data.txt")

    # Step 2: Embedding
    embedding_dim = 128
    embedder = Embedding(vocab_size=len(vocab), embedding_dim=embedding_dim)
    sentence_ids = indexed_data[0] if indexed_data else []
    embedded = embedder.embed_sentence(sentence_ids)

    print("\n=== Embedding ===")
    #for vec in embedded:
    #    print(vec)

    # Step 3: Positional Encoding
    pos_encoded = encode_sentence_positions(len(sentence_ids), embedding_dim)

    print("\n=== Positional Encoding ===")
    #for vec in pos_encoded:
    #    print(vec)

    # Combine embeddings and position encodings
    encoder_input = [[e + p for e, p in zip(embed, pos)]
                     for embed, pos in zip(embedded, pos_encoded)]

    print("\n=== Encoder Input ===")
    #for vec in encoder_input:
    #    print(vec)

    # Step 4: Encoder Forward Pass
    encoder = Encoder(num_layers=2, d_model=embedding_dim, d_ff=embedding_dim * 4, residual_scale=1.0)
    encoder_output = encoder.forward(encoder_input)

    print("\n=== Encoder Output ===")
    #for vec in encoder_output:
    #    print(vec)

    from classifier.classifier import Classifier
    from utils.math_ops import softmax

    # Step 5: Classification
    num_classes = 2  # Change this based on your task
    classifier = Classifier(input_dim=embedding_dim, num_classes=num_classes)
    logits = classifier.forward(encoder_output)

    print("\n=== Classifier Output (Logits) ===")
    print(logits)
    print(f"Logit difference: {abs(logits[0] - logits[1])}")

    # Debug: Check encoder output range
    print(f"\n=== Encoder Output Stats ===")
    if encoder_output:
        flat_values = [val for vec in encoder_output for val in vec]
        print(f"Min: {min(flat_values):.6f}, Max: {max(flat_values):.6f}")
        print(f"Mean: {sum(flat_values)/len(flat_values):.6f}")

    # Step 6: Softmax (Optional)
    probs = softmax(logits)
    print("\n=== Probabilities ===")
    print(probs)


if __name__ == "__main__":
    main()
