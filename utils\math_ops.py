# Custom matrix operations (dot, softmax, etc.)
# utils/math_ops.py
import random
import math

def dot_product(a, b):
    return sum(x * y for x, y in zip(a, b))

def matmul(A, B):
    # Multiply matrix A (m×n) with matrix B (n×p)
    result = []
    for i in range(len(A)):
        row = []
        for j in range(len(B[0])):
            val = 0
            for k in range(len(B)):
                val += A[i][k] * B[k][j]
            row.append(val)
        result.append(row)
    return result

def transpose(matrix):
    return [[row[i] for row in matrix] for i in range(len(matrix[0]))]

def add_vectors(a, b):
    return [x + y for x, y in zip(a, b)]

def add_matrices(A, B):
    return [[a + b for a, b in zip(row_a, row_b)] for row_a, row_b in zip(A, B)]

def scalar_multiply_vector(vector, scalar):
    return [x * scalar for x in vector]

def scalar_multiply_matrix(matrix, scalar):
    return [[x * scalar for x in row] for row in matrix]

def relu(vector):
    return [max(0, x) for x in vector]

def softmax(vector):
    max_val = max(vector)  # for numerical stability
    exp_vals = [math.exp(x - max_val) for x in vector]
    sum_exp = sum(exp_vals)
    return [x / sum_exp for x in exp_vals]

def initialize_matrix(rows, cols, value=0.01):
    # Fill matrix with fixed small value or random value later
    #return [[value for _ in range(cols)] for _ in range(rows)]
    return [[random.uniform(-0.1, 0.1) for _ in range(cols)] for _ in range(rows)]

def print_matrix(mat, name="Matrix"):
    print(f"\n{name}:")
    for row in mat:
        print(row)

def scale_vector(vector, scalar):
    return [x * scalar for x in vector]

def accuracy(predictions, targets):
    correct = sum(1 for p, t in zip(predictions, targets) if p == t)
    return correct / len(targets)

def cross_entropy_loss(logits, target_class):
    probs = softmax(logits)
    return -math.log(probs[target_class] + 1e-8)

def mean_squared_error(pred, target):
    return sum((p - t) ** 2 for p, t in zip(pred, target)) / len(pred)


