# utils/math_ops.py

import random
import math

def dot_product(a, b):
    return sum(x * y for x, y in zip(a, b))

def matmul(A, B):
    result = []
    for i in range(len(A)):
        row = []
        for j in range(len(B[0])):
            val = 0
            for k in range(len(B)):
                val += A[i][k] * B[k][j]
            row.append(val)
        result.append(row)
    return result

def batch_matmul(batch_A, batch_B):
    # Each batch_A[i] and batch_B[i] are 2D matrices
    return [matmul(A, B) for A, B in zip(batch_A, batch_B)]

def transpose(matrix):
    return [[row[i] for row in matrix] for i in range(len(matrix[0]))]

def add_vectors(a, b):
    return [x + y for x, y in zip(a, b)]

def add_matrices(A, B):
    return [[a + b for a, b in zip(row_a, row_b)] for row_a, row_b in zip(A, B)]

def scalar_multiply_vector(vector, scalar):
    return [x * scalar for x in vector]

def scalar_multiply_matrix(matrix, scalar):
    return [[x * scalar for x in row] for row in matrix]

def relu(vector):
    return [max(0, x) for x in vector]

def backward_relu(grad_output, input_vector):
    return [g if x > 0 else 0 for g, x in zip(grad_output, input_vector)]

def softmax(vector):
    max_val = max(vector)  # Numerical stability
    exp_vals = [math.exp(x - max_val) for x in vector]
    sum_exp = sum(exp_vals)
    return [x / sum_exp for x in exp_vals]

def batch_softmax(batch):
    return [softmax(vec) for vec in batch]

def backward_softmax(softmax_output, target_index):
    grad = [0.0] * len(softmax_output)
    for i in range(len(softmax_output)):
        grad[i] = softmax_output[i]
    grad[target_index] -= 1  # Cross-entropy with softmax
    return grad

def initialize_matrix(rows, cols, value=0.01):
    return [[random.uniform(-0.1, 0.1) for _ in range(cols)] for _ in range(rows)]

def print_matrix(mat, name="Matrix"):
    print(f"\n{name}:")
    for row in mat:
        print(row)

def scale_vector(vector, scalar):
    return [x * scalar for x in vector]

def accuracy(predictions, targets):
    correct = sum(1 for p, t in zip(predictions, targets) if p == t)
    return correct / len(targets)

def cross_entropy_loss(logits, target_class):
    probs = softmax(logits)
    return -math.log(probs[target_class] + 1e-8)

def mean_squared_error(pred, target):
    return sum((p - t) ** 2 for p, t in zip(pred, target)) / len(pred)

def layer_norm(vector, epsilon=1e-6):
    mean = sum(vector) / len(vector)
    variance = sum((x - mean) ** 2 for x in vector) / len(vector)
    return [(x - mean) / math.sqrt(variance + epsilon) for x in vector]

def batch_norm(batch, epsilon=1e-6):
    # batch is a list of vectors
    n = len(batch)
    d = len(batch[0])
    mean = [sum(vec[i] for vec in batch) / n for i in range(d)]
    var = [sum((vec[i] - mean[i])**2 for vec in batch) / n for i in range(d)]
    normed = []
    for vec in batch:
        normed.append([(vec[i] - mean[i]) / math.sqrt(var[i] + epsilon) for i in range(d)])
    return normed
def split_heads(x, num_heads):
    # x: [seq_len x d_model]
    seq_len = len(x)
    d_model = len(x[0])
    depth = d_model // num_heads
    heads = [[] for _ in range(num_heads)]
    for token in x:
        for i in range(num_heads):
            heads[i].append(token[i*depth:(i+1)*depth])
    return heads

def combine_heads(heads):
    # heads: list of [seq_len x depth]
    seq_len = len(heads[0])
    combined = []
    for i in range(seq_len):
        combined.append([val for head in heads for val in head[i]])
    return combined
