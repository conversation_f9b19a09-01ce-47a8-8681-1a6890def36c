# tokenizer.py

SPECIAL_TOKENS = ["<PAD>", "<UNK>", "<START>", "<END>"]

def clean_text(text):
    punctuations = '.,!?;:()[]{}"\''
    cleaned = ''
    for ch in text.lower():
        if ch not in punctuations:
            cleaned += ch
        else:
            cleaned += ' '
    return cleaned

def tokenize(text):
    return text.strip().split()

def build_vocab(tokenized_sentences):
    vocab = {}
    reverse_vocab = {}
    idx = 0

    # Add special tokens first
    for token in SPECIAL_TOKENS:
        vocab[token] = idx
        reverse_vocab[idx] = token
        idx += 1

    for sentence in tokenized_sentences:
        for word in sentence:
            if word not in vocab:
                vocab[word] = idx
                reverse_vocab[idx] = word
                idx += 1
    return vocab, reverse_vocab

def encode(tokenized_sentences, vocab):
    indexed_data = []
    unk_token = vocab.get("<UNK>")
    start_token = vocab.get("<START>")
    end_token = vocab.get("<END>")

    for sentence in tokenized_sentences:
        # Wrap with <START> and <END>
        indexed_sentence = [start_token]
        for word in sentence:
            indexed_sentence.append(vocab.get(word, unk_token))
        indexed_sentence.append(end_token)
        indexed_data.append(indexed_sentence)

    return indexed_data

def process_file(path):
    with open(path, "r", encoding="utf-8") as f:
        lines = f.readlines()

    tokenized_sentences = []
    for line in lines:
        cleaned = clean_text(line)
        tokens = tokenize(cleaned)
        if tokens:
            tokenized_sentences.append(tokens)

    vocab, reverse_vocab = build_vocab(tokenized_sentences)
    indexed_data = encode(tokenized_sentences, vocab)

    return tokenized_sentences, vocab, reverse_vocab, indexed_data
