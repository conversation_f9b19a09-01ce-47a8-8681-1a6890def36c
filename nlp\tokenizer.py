# Tokenizer + vocabulary builder
# tokenizer.py

def clean_text(text):
    # Lowercase and remove punctuation (basic)
    punctuations = '.,!?;:()[]{}"\''
    cleaned = ''
    for ch in text.lower():
        if ch not in punctuations:
            cleaned += ch
        else:
            cleaned += ' '
    return cleaned

def tokenize(text):
    # Split by whitespace
    return text.strip().split()

def build_vocab(tokenized_sentences):
    vocab = {}
    reverse_vocab = {}
    idx = 0
    for sentence in tokenized_sentences:
        for word in sentence:
            if word not in vocab:
                vocab[word] = idx
                reverse_vocab[idx] = word
                idx += 1
    return vocab, reverse_vocab

def encode(tokenized_sentences, vocab):
    indexed_data = []
    for sentence in tokenized_sentences:
        indexed_sentence = [vocab[word] for word in sentence]
        indexed_data.append(indexed_sentence)
    return indexed_data

def process_file(path):
    with open(path, "r", encoding="utf-8") as f:

        lines = f.readlines()

    tokenized_sentences = []
    for line in lines:
        cleaned = clean_text(line)
        tokens = tokenize(cleaned)
        if tokens:
            tokenized_sentences.append(tokens)

    vocab, reverse_vocab = build_vocab(tokenized_sentences)
    indexed_data = encode(tokenized_sentences, vocab)

    return tokenized_sentences, vocab, reverse_vocab, indexed_data
