# tokenizer.py

SPECIAL_TOKENS = ["<PAD>", "<UNK>", "<START>", "<END>"]

def clean_text(text):
    punctuations = '.,!?;:()[]{}"\''
    cleaned = ''
    for ch in text.lower():
        if ch not in punctuations:
            cleaned += ch
        else:
            cleaned += ' '
    return cleaned

def tokenize(text):
    return text.strip().split()

def count_words(tokenized_sentences):
    word_counts = {}
    for sentence in tokenized_sentences:
        for word in sentence:
            if word in word_counts:
                word_counts[word] += 1
            else:
                word_counts[word] = 1
    return word_counts

def build_vocab(tokenized_sentences, max_vocab_size=None, min_freq=1):
    vocab = {}
    reverse_vocab = {}
    idx = 0

    # Add special tokens first
    for token in SPECIAL_TOKENS:
        vocab[token] = idx
        reverse_vocab[idx] = token
        idx += 1

    word_counts = count_words(tokenized_sentences)

    # Filter words by min_freq
    filtered = []
    for word, count in word_counts.items():
        if count >= min_freq:
            filtered.append((word, count))

    # Sort by frequency (descending)
    filtered.sort(key=lambda x: -x[1])

    # Limit vocab size if specified
    if max_vocab_size:
        filtered = filtered[:max_vocab_size - len(SPECIAL_TOKENS)]

    for word, _ in filtered:
        if word not in vocab:
            vocab[word] = idx
            reverse_vocab[idx] = word
            idx += 1

    return vocab, reverse_vocab

def encode(tokenized_sentences, vocab):
    indexed_data = []
    unk_token = vocab.get("<UNK>")
    start_token = vocab.get("<START>")
    end_token = vocab.get("<END>")

    for sentence in tokenized_sentences:
        indexed_sentence = [start_token]
        for word in sentence:
            indexed_sentence.append(vocab.get(word, unk_token))
        indexed_sentence.append(end_token)
        indexed_data.append(indexed_sentence)

    return indexed_data

def decode(indexed_sentence, reverse_vocab):
    return ' '.join([reverse_vocab.get(idx, "<UNK>") for idx in indexed_sentence])

def pad_sequences(sequences, pad_token_id, max_len=None):
    if not max_len:
        max_len = max(len(seq) for seq in sequences)
    return [seq + [pad_token_id] * (max_len - len(seq)) for seq in sequences]

def save_vocab(vocab, path="vocab.txt"):
    with open(path, "w", encoding="utf-8") as f:
        for word, idx in vocab.items():
            f.write(f"{word}\t{idx}\n")

def load_vocab(path="vocab.txt"):
    vocab = {}
    reverse_vocab = {}
    with open(path, "r", encoding="utf-8") as f:
        for line in f:
            word, idx = line.strip().split('\t')
            idx = int(idx)
            vocab[word] = idx
            reverse_vocab[idx] = word
    return vocab, reverse_vocab

def process_file(path, max_vocab_size=None, min_freq=1):
    with open(path, "r", encoding="utf-8") as f:
        lines = f.readlines()

    tokenized_sentences = []
    for line in lines:
        cleaned = clean_text(line)
        tokens = tokenize(cleaned)
        if tokens:
            tokenized_sentences.append(tokens)

    vocab, reverse_vocab = build_vocab(tokenized_sentences, max_vocab_size=max_vocab_size, min_freq=min_freq)
    indexed_data = encode(tokenized_sentences, vocab)

    return tokenized_sentences, vocab, reverse_vocab, indexed_data
