import math

MAX_SEQUENCE_LENGTH = 512  # Safeguard against excessive positions

# Simple cache to store precomputed encodings
_positional_cache = {}

def get_positional_encoding(position, d_model):
    """
    Returns a list of size d_model representing positional encoding for one token position.
    Uses fixed sinusoidal formula with caching.
    """
    if position >= MAX_SEQUENCE_LENGTH:
        raise ValueError(f"Position {position} exceeds MAX_SEQUENCE_LENGTH {MAX_SEQUENCE_LENGTH}")

    key = (position, d_model)
    if key in _positional_cache:
        return _positional_cache[key]

    encoding = []
    for i in range(d_model):
        angle = position / (10000 ** (2 * (i // 2) / d_model))
        encoding.append(math.sin(angle) if i % 2 == 0 else math.cos(angle))

    _positional_cache[key] = encoding
    return encoding

def encode_sentence_positions(sentence_length, d_model, scale=True):
    """
    Returns positional encoding for a full sentence of given length.
    Output: List of vectors (each of size d_model)
    scale: If True, multiply by sqrt(d_model) for better gradient flow
    """
    if sentence_length > MAX_SEQUENCE_LENGTH:
        raise ValueError(f"Sentence length {sentence_length} exceeds MAX_SEQUENCE_LENGTH {MAX_SEQUENCE_LENGTH}")

    positions = [get_positional_encoding(pos, d_model) for pos in range(sentence_length)]

    if scale:
        scale_factor = math.sqrt(d_model)
        positions = [[val * scale_factor for val in pos] for pos in positions]

    return positions

def encode_batch_positions(batch_lengths, d_model, scale=True):
    """
    For a list of sentence lengths in a batch, return a list of positional encodings
    Each element in the output is a list of vectors for one sentence.
    """
    return [encode_sentence_positions(length, d_model, scale) for length in batch_lengths]

def init_learnable_positional_encoding(max_len, d_model):
    """
    Returns a learnable (zero-initialized) positional encoding matrix for training
    """
    return [[0.0 for _ in range(d_model)] for _ in range(max_len)]
