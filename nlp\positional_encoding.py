# Manual positional encoding
# nlp/positional_encoding.py

import math

def get_positional_encoding(position, d_model):
    """
    Returns a list of size d_model representing positional encoding for one token position
    """
    encoding = []
    for i in range(d_model):
        angle = position / (10000 ** (2 * (i // 2) / d_model))
        if i % 2 == 0:
            encoding.append(math.sin(angle))
        else:
            encoding.append(math.cos(angle))
    return encoding

def encode_sentence_positions(sentence_length, d_model):
    """
    Returns positional encoding for a full sentence of given length
    Output: List of vectors (each of size d_model)
    """
    return [get_positional_encoding(pos, d_model) for pos in range(sentence_length)]
