# transformer/self_attention.py

from utils.math_ops import dot_product, softmax, scale_vector, add_vectors

class SelfAttention:
    def __init__(self, d_model):
        self.d_model = d_model

        # Weight matrices for query, key, value (d_model × d_model)
        self.W_q = [[0.01 for _ in range(d_model)] for _ in range(d_model)]
        self.W_k = [[0.01 for _ in range(d_model)] for _ in range(d_model)]
        self.W_v = [[0.01 for _ in range(d_model)] for _ in range(d_model)]

    def linear(self, x, W):
        # x: vector, W: matrix
        result = []
        for j in range(len(W[0])):  # output dim
            val = 0
            for i in range(len(x)):
                val += x[i] * W[i][j]
            result.append(val)
        return result

    def compute_attention(self, queries, keys, values):
        scores = []
        for q in queries:
            row = []
            for k in keys:
                row.append(dot_product(q, k) / (self.d_model ** 0.5))  # scaled dot-product
            scores.append(row)

        attention_weights = [softmax(score_row) for score_row in scores]

        output = []
        for i, weights in enumerate(attention_weights):
            context = [0] * len(values[0])
            for j, weight in enumerate(weights):
                scaled_value = scale_vector(values[j], weight)
                context = add_vectors(context, scaled_value)
            output.append(context)

        return output

    def forward(self, inputs):
        # inputs: list of token vectors (sequence)
        queries = [self.linear(x, self.W_q) for x in inputs]
        keys    = [self.linear(x, self.W_k) for x in inputs]
        values  = [self.linear(x, self.W_v) for x in inputs]

        return self.compute_attention(queries, keys, values)
