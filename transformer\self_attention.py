from utils.math_ops import dot_product, softmax, scale_vector, add_vectors, transpose
import math
import random

class SelfAttention:
    def __init__(self, d_model, num_heads=1, causal=False):
        self.d_model = d_model
        self.num_heads = num_heads
        self.head_dim = d_model // num_heads
        self.causal = causal

        # Xavier initialization
        def xavier_init(in_dim, out_dim):
            scale = math.sqrt(6 / (in_dim + out_dim))
            return [[random.uniform(-scale, scale) for _ in range(out_dim)] for _ in range(in_dim)]

        self.W_q = [xavier_init(d_model, self.head_dim) for _ in range(num_heads)]
        self.W_k = [xavier_init(d_model, self.head_dim) for _ in range(num_heads)]
        self.W_v = [xavier_init(d_model, self.head_dim) for _ in range(num_heads)]

        # Output projection (concat heads -> d_model)
        self.W_o = xavier_init(d_model, d_model)

    def linear(self, x, W):
        result = []
        for j in range(len(W[0])):  # output dim
            val = sum(x[i] * W[i][j] for i in range(len(x)))
            result.append(val)
        return result

    def apply_mask(self, scores):
        """
        Causal mask: Prevent position i from attending to j > i.
        """
        for i in range(len(scores)):
            for j in range(len(scores[i])):
                if j > i:
                    scores[i][j] = float('-inf')
        return scores

    def compute_attention(self, queries, keys, values):
        scores = []
        for q in queries:
            row = []
            for k in keys:
                row.append(dot_product(q, k) / math.sqrt(len(q)))
            scores.append(row)

        if self.causal:
            scores = self.apply_mask(scores)

        attention_weights = [softmax(score_row) for score_row in scores]

        # (Optional) Save attention weights for visualization
        self.last_attention_weights = attention_weights

        output = []
        for i, weights in enumerate(attention_weights):
            context = [0] * len(values[0])
            for j, weight in enumerate(weights):
                context = add_vectors(context, scale_vector(values[j], weight))
            output.append(context)

        return output

    def forward(self, inputs):
        """
        inputs: list of d_model-dimensional vectors (sequence)
        returns: list of d_model-dimensional vectors (sequence)
        """
        all_heads_output = []

        for h in range(self.num_heads):
            q = [self.linear(x, self.W_q[h]) for x in inputs]
            k = [self.linear(x, self.W_k[h]) for x in inputs]
            v = [self.linear(x, self.W_v[h]) for x in inputs]
            head_output = self.compute_attention(q, k, v)
            all_heads_output.append(head_output)

        # Concatenate heads
        concat = []
        for i in range(len(inputs)):
            token_concat = []
            for h in range(self.num_heads):
                token_concat += all_heads_output[h][i]
            concat.append(token_concat)

        # Output projection
        projected = [self.linear(token_concat, self.W_o) for token_concat in concat]
        return projected
