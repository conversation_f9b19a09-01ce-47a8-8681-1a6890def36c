# classifier/classifier.py

import math
import random

class Classifier:
    def __init__(self, input_dim, num_classes=2, pooling="mean", hidden_dim=None, class_weights=None):
        self.input_dim = input_dim
        self.num_classes = num_classes
        self.pooling = pooling
        self.hidden_dim = hidden_dim or input_dim  # Default to same size if not specified
        self.training = True  # Set this flag externally to enable dropout
        self.class_weights = class_weights  # Optional list of weights per class

        # Xavier initialization
        lim1 = math.sqrt(6 / (input_dim + self.hidden_dim)) * 0.5
        self.w1 = [[random.uniform(-lim1, lim1) for _ in range(input_dim)] for _ in range(self.hidden_dim)]
        self.b1 = [0.0 for _ in range(self.hidden_dim)]

        lim2 = math.sqrt(6 / (self.hidden_dim + num_classes)) * 0.5
        self.w2 = [[random.uniform(-lim2, lim2) for _ in range(self.hidden_dim)] for _ in range(num_classes)]
        self.b2 = [0.0 for _ in range(num_classes)]

        # For attention pooling
        self.attn_query = [random.uniform(-0.1, 0.1) for _ in range(input_dim)]

    def _mean_pooling(self, encoder_output):
        pooled = [0.0 for _ in range(self.input_dim)]
        for i in range(self.input_dim):
            pooled[i] = sum(vec[i] for vec in encoder_output) / len(encoder_output)
        return pooled

    def _max_pooling(self, encoder_output):
        return [max(vec[i] for vec in encoder_output) for i in range(self.input_dim)]

    def _attention_pooling(self, encoder_output):
        def dot(u, v):
            return sum(ui * vi for ui, vi in zip(u, v))

        scores = [dot(self.attn_query, vec) for vec in encoder_output]
        exp_scores = [math.exp(s) for s in scores]
        total = sum(exp_scores)
        weights = [s / total for s in exp_scores]

        pooled = [0.0 for _ in range(self.input_dim)]
        for vec, w in zip(encoder_output, weights):
            for i in range(self.input_dim):
                pooled[i] += vec[i] * w
        return pooled

    def _dropout(self, vec, rate=0.1):
        return [x * (1 if random.random() > rate else 0) for x in vec]

    def _tanh(self, vec):
        return [math.tanh(x) for x in vec]

    def _linear(self, vec, weights, biases):
        output = []
        for j in range(len(weights)):
            val = sum(w * v for w, v in zip(weights[j], vec)) + biases[j]
            output.append(val)
        return output

    def forward(self, encoder_output):
        if not encoder_output:
            return [0.0 for _ in range(self.num_classes)]

        # Pooling
        if self.pooling == "max":
            pooled = self._max_pooling(encoder_output)
        elif self.pooling == "attention":
            pooled = self._attention_pooling(encoder_output)
        else:
            pooled = self._mean_pooling(encoder_output)

        # Dropout
        if self.training:
            pooled = self._dropout(pooled)

        # First layer + activation
        hidden = self._linear(pooled, self.w1, self.b1)
        hidden = self._tanh(hidden)

        # Second layer (output)
        logits = self._linear(hidden, self.w2, self.b2)

        # Optional class weighting
        if self.class_weights:
            logits = [logit * self.class_weights[i] for i, logit in enumerate(logits)]

        # Logit clipping
        max_logit = 3.0
        logits = [max(-max_logit, min(max_logit, logit)) for logit in logits]

        return logits
