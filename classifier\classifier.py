# classifier/classifier.py

import math
import random

class Classifier:
    def __init__(self, input_dim, num_classes=2):
        # Initialize weights for a single-layer classifier
        self.input_dim = input_dim
        self.num_classes = num_classes

        # Xavier initialization for better gradient flow
        limit = math.sqrt(6 / (input_dim + num_classes))
        self.weights = [[random.uniform(-limit, limit) for _ in range(input_dim)] for _ in range(num_classes)]
        self.biases = [0.0 for _ in range(num_classes)]

    def forward(self, encoder_output):
        # Use [CLS] token approach or mean pooling instead
        if not encoder_output:
            return [0.0 for _ in range(self.num_classes)]

        # Mean pooling is more stable than max pooling
        pooled = [0.0 for _ in range(self.input_dim)]
        for i in range(self.input_dim):
            pooled[i] = sum(vec[i] for vec in encoder_output) / len(encoder_output)
        
        # Add dropout simulation (random zeroing)
        if hasattr(self, 'training') and self.training:
            pooled = [x * (1 if random.random() > 0.1 else 0) for x in pooled]
        
        # Linear layer with better initialization
        logits = []
        for class_idx in range(self.num_classes):
            logit = sum(w * x for w, x in zip(self.weights[class_idx], pooled)) + self.biases[class_idx]
            logits.append(logit)

        # Clip logits to prevent extreme values
        max_logit = 10.0  # Prevent logit differences > 20
        logits = [max(-max_logit, min(max_logit, logit)) for logit in logits]

        return logits

